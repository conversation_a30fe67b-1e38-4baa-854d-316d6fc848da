using UnityEngine;

public class Projectile : MonoBehaviour
{
    public float speed = 50f; // Speed of the projectile
    public float lifetime = 5f; // How long the projectile exists before being destroyed
    public float damage = 10f; // Damage the projectile deals

    private Rigidbody rb;

    void Awake()
    {
        rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            Debug.LogWarning("Projectile: No Rigidbody found on projectile prefab. Adding one.");
            rb = gameObject.AddComponent<Rigidbody>();
            rb.useGravity = false; // Projectiles usually don't use gravity
        }
    }

    void Start()
    {
        // Set the initial velocity based on the forward direction of the projectile
        // This assumes the projectile's forward (Z-axis) is aligned with its intended direction
        if (rb != null)
        {
            rb.velocity = transform.forward * speed;
        }
        else
        {
            Debug.LogError("Projectile: Rigidbody is null, cannot set initial velocity.");
        }

        // Destroy the projectile after its lifetime
        Destroy(gameObject, lifetime);
    }

    void OnTriggerEnter(Collider other)
    {
        // Example: Apply damage to a target on collision
        // Target target = other.GetComponent<Target>();
        // if (target != null)
        // {
        //     target.TakeDamage(damage);
        // }

        // Destroy the projectile on collision
        Destroy(gameObject);
    }
}
