using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class WeaponStats
{
    public string weaponName;
    public int maxAmmoInClip;
    public int currentAmmoInClip;
    public int totalReserveAmmo;
    public int maxReserveAmmo;
    public float reloadTime;
    public bool isReloading;
    public float fireRate; // Moved from WeaponHolder for better encapsulation
    public float damage; // Moved from WeaponHolder for better encapsulation
    public GameObject projectilePrefab; // For projectile-based weapons
    public float projectileSpeed;
    public List<AudioClip> shootSounds; // New: List of sounds played randomly when shooting
    public bool hasEquipOverride; // New: Does this weapon have an "Equip_Override" animation?
    public GameObject weaponRigGameObject; // New: Reference to the GameObject containing the weapon's rig
    public Transform aimingPositionReference; // New: Reference to a Transform for aiming position
    public Transform aimingRightHandIKTargetReference; // New: Reference to a Transform for right hand IK aiming position
    public Transform aimingLeftHandIKTargetReference; // New: Reference to a Transform for left hand IK aiming position
}

public class InventoryManager : MonoBehaviour
{
    [Header("Weapon Inventory")]
    [SerializeField] private List<WeaponStats> weaponInventory = new List<WeaponStats>();
    [SerializeField] private int currentWeaponIndex = -1; // Matches WeaponHolder's index

    public delegate void AmmoChanged(int currentClip, int totalAmmo);
    public event AmmoChanged OnAmmoChanged;

    public delegate void ReloadStarted();
    public event ReloadStarted OnReloadStarted;

    public delegate void ReloadFinished();
    public event ReloadFinished OnReloadFinished;

    private Coroutine reloadCoroutine;

    void Start()
    {
        // Initialize current ammo in clip to max for all weapons
        foreach (var weapon in weaponInventory)
        {
            weapon.currentAmmoInClip = weapon.maxAmmoInClip;
        }
        UpdateAmmoDisplay();
    }

    public void SetCurrentWeapon(int index)
    {
        currentWeaponIndex = index;
        UpdateAmmoDisplay();
    }

    public WeaponStats GetCurrentWeaponStats()
    {
        if (currentWeaponIndex >= 0 && currentWeaponIndex < weaponInventory.Count)
        {
            return weaponInventory[currentWeaponIndex];
        }
        return null;
    }

    public bool CanShoot()
    {
        WeaponStats currentStats = GetCurrentWeaponStats();
        if (currentStats == null) return false;

        return currentStats.currentAmmoInClip > 0 && !currentStats.isReloading;
    }

    public void ConsumeAmmo(int amount = 1)
    {
        WeaponStats currentStats = GetCurrentWeaponStats();
        if (currentStats != null && currentStats.currentAmmoInClip >= amount)
        {
            currentStats.currentAmmoInClip -= amount;
            UpdateAmmoDisplay();
        }
    }

    public void StartReload()
    {
        WeaponStats currentStats = GetCurrentWeaponStats();
        if (currentStats == null || currentStats.isReloading) return;

        if (currentStats.currentAmmoInClip == currentStats.maxAmmoInClip || currentStats.totalReserveAmmo == 0)
        {
            Debug.Log("No need to reload or no reserve ammo.");
            return;
        }

        if (reloadCoroutine != null)
        {
            StopCoroutine(reloadCoroutine);
        }
        reloadCoroutine = StartCoroutine(ReloadCoroutine(currentStats));
    }

    private System.Collections.IEnumerator ReloadCoroutine(WeaponStats stats)
    {
        stats.isReloading = true;
        Debug.Log($"InventoryManager: Invoking OnReloadStarted for {stats.weaponName}.");
        OnReloadStarted?.Invoke();
        Debug.Log("Reloading " + stats.weaponName + "...");

        yield return new WaitForSeconds(stats.reloadTime);

        int ammoNeeded = stats.maxAmmoInClip - stats.currentAmmoInClip;
        int ammoToTakeFromReserve = Mathf.Min(ammoNeeded, stats.totalReserveAmmo);

        stats.currentAmmoInClip += ammoToTakeFromReserve;
        stats.totalReserveAmmo -= ammoToTakeFromReserve;

        stats.isReloading = false;
        OnReloadFinished?.Invoke();
        UpdateAmmoDisplay();
        Debug.Log(stats.weaponName + " Reloaded! Clip: " + stats.currentAmmoInClip + "/" + stats.maxAmmoInClip + ", Reserve: " + stats.totalReserveAmmo);
    }

    public void AddAmmo(string weaponName, int amount)
    {
        foreach (var weapon in weaponInventory)
        {
            if (weapon.weaponName == weaponName)
            {
                weapon.totalReserveAmmo = Mathf.Min(weapon.totalReserveAmmo + amount, weapon.maxReserveAmmo);
                UpdateAmmoDisplay();
                Debug.Log("Added " + amount + " ammo to " + weaponName + ". Total reserve: " + weapon.totalReserveAmmo);
                return;
            }
        }
        Debug.LogWarning("Weapon " + weaponName + " not found in inventory to add ammo.");
    }

    private void UpdateAmmoDisplay()
    {
        WeaponStats currentStats = GetCurrentWeaponStats();
        if (currentStats != null)
        {
            OnAmmoChanged?.Invoke(currentStats.currentAmmoInClip, currentStats.totalReserveAmmo);
        }
        else
        {
            OnAmmoChanged?.Invoke(0, 0); // No weapon equipped
        }
    }
}
