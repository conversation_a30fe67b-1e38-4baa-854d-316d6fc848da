using UnityEngine;
using System.Collections.Generic;
using UnityEngine.Animations.Rigging; // Required for Rig component

public class WeaponHolder : MonoBehaviour
{
    [Header("Weapon Prefabs")]
    [SerializeField] private GameObject[] weaponsInHand; // Array of pre-placed weapon GameObjects to manage (activate/deactivate)
    [SerializeField] private float knifeRange = 1.5f; // Range of knife slash
    [SerializeField] private float recoilAnimationDuration = 0.2f; // Duration of the recoil animation
    [Header("Weapon Recoil Settings")]
    [SerializeField] private Vector3 recoilKickBack = new Vector3(0f, 0.02f, -0.1f); // How much the weapon kicks back (positional)
    [SerializeField] private Vector3 recoilRotation = new Vector3(5f, 0f, 0f); // How much the weapon rotates (rotational, e.g., up)
    [SerializeField] private float recoilRecoverySpeed = 10f; // Speed at which the weapon returns to original position
    [SerializeField] private float rigWeightTransitionSpeed = 5f; // Speed at which rig weights transition

    [Header("Weapon Switching Settings")]
    [SerializeField] private float scrollCooldown = 0.5f; // Time in seconds between weapon scrolls

    [Header("Weapon Grip Settings")]
    [SerializeField] private Transform weaponSocket; // The socket in the hand where the weapon should attach

    private GameObject currentWeapon;
    private int currentWeaponIndex = -1; // Default to no weapon (-1)
    private float nextFireTime = 0f;
    private float lastScrollTime = 0f; // Time when the last scroll occurred

    private Vector3 defaultWeaponLocalPosition; // Stores the weapon's default local position
    private Quaternion defaultWeaponLocalRotation; // Stores the weapon's default local rotation
    [SerializeField] private Transform rightHandIKTarget; // IK target for right hand to move with recoil
    [SerializeField] private Transform leftHandIKTarget; // IK target for left hand to move with recoil
    private Vector3 defaultRightIKTargetPosition; // Stores the right IK target's default local position
    private Quaternion defaultRightIKTargetRotation; // Stores the right IK target's default local rotation
    private Vector3 defaultLeftIKTargetPosition; // Stores the left IK target's default local position
    private Quaternion defaultLeftIKTargetRotation; // Stores the left IK target's default local rotation

    private Vector3 currentWeaponTargetLocalPosition; // Target position for weapon (aiming or default)
    private Quaternion currentWeaponTargetLocalRotation; // Target rotation for weapon (aiming or default)
    private Vector3 currentRightIKTargetLocalPosition; // Target position for right hand IK (aiming or default)
    private Quaternion currentRightIKTargetLocalRotation; // Target rotation for right hand IK (aiming or default)
    private Vector3 currentLeftIKTargetLocalPosition; // Target position for left hand IK (aiming or default)
    private Quaternion currentLeftIKTargetLocalRotation; // Target rotation for left hand IK (aiming or default)

    [Header("Aiming Settings")]
    [SerializeField] private float aimTransitionSpeed = 10f; // Speed at which weapon moves to aiming position

    private PlayerController playerController; // Reference to PlayerController
    private Animator playerAnimator; // Reference to the Player's Animator
    private Animator weaponAnimator; // Reference to the current Weapon's Animator
    private InventoryManager inventoryManager; // Reference to InventoryManager

    private Coroutine recoilCoroutine; // To manage recoil animation
    private Coroutine rigWeightCoroutine; // To manage rig weight transitions
    private bool isRecoiling = false; // Flag to indicate if recoil animation is active

    // playerCamera is now obtained from PlayerController, no need for [SerializeField] here
    private Transform playerCamera; 

    void Start()
    {
        playerController = GetComponentInParent<PlayerController>();
        if (playerController == null)
        {
            Debug.LogError("PlayerController not found in parent!");
        }
        else
        {
            // Get the playerCamera from the PlayerController
            playerCamera = playerController.playerCamera;
            if (playerCamera == null)
            {
                Debug.LogWarning("Player Camera not assigned in PlayerController. Recoil animation might not work.");
            }
        }

        playerAnimator = GetComponentInParent<Animator>();
        if (playerAnimator == null)
        {
            Debug.LogError("Animator not found in parent!");
        }

        // weaponAnimator will be set in EquipWeapon
        weaponAnimator = null;

        inventoryManager = GetComponentInParent<InventoryManager>();
        if (inventoryManager == null)
        {
            Debug.LogError("InventoryManager not found in parent!");
        }
        else
        {
            inventoryManager.OnReloadStarted += OnReloadStartedHandler;
            inventoryManager.OnReloadFinished += OnReloadFinishedHandler; // Subscribe to reload finished event
        }

        // Ensure a main camera with the "MainCamera" tag exists in the scene
        if (Camera.main == null)
        {
            Debug.LogError("No camera with 'MainCamera' tag found in the scene! Please ensure your Cinemachine camera is tagged as 'MainCamera'.");
        }

        // Ensure all weapons in the array are initially disabled
        foreach (GameObject weapon in weaponsInHand)
        {
            if (weapon != null)
            {
                // Assuming weapons are already correctly positioned in the hierarchy
                // and just need to be disabled initially.
                weapon.SetActive(false);
            }
        }

        // Equip no weapon by default
        EquipWeapon(currentWeaponIndex);
    }

    void Update()
    {
        // Handle weapon switching input
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            EquipWeapon(0); // Equip first weapon (Pistol)
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            EquipWeapon(1); // Equip second weapon (Knife)
        }

        // Handle scroll wheel weapon switching with cooldown
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f && Time.time >= lastScrollTime + scrollCooldown)
        {
            int newIndex = currentWeaponIndex;

            if (scroll > 0f) // Scroll Up
            {
                newIndex++;
                if (newIndex >= weaponsInHand.Length)
                {
                    newIndex = -1; // Switch to empty hands if scrolling up from last weapon
                }
            }
            else if (scroll < 0f) // Scroll Down
            {
                newIndex--;
                if (newIndex < -1)
                {
                    newIndex = weaponsInHand.Length - 1; // Switch to last weapon if scrolling down from empty hands
                }
            }

            if (newIndex != currentWeaponIndex)
            {
                EquipWeapon(newIndex);
                lastScrollTime = Time.time; // Update last scroll time
            }
        }

        // Handle reload input
        if (Input.GetKeyDown(KeyCode.R) && inventoryManager != null)
        {
            inventoryManager.StartReload();
        }
    }



    void EquipWeapon(int index)
    {
        // Get current weapon stats before deactivating
        WeaponStats previousWeaponStats = inventoryManager?.GetCurrentWeaponStats();

        // Deactivate the current weapon if one is equipped
        if (currentWeapon != null)
        {
            currentWeapon.SetActive(false);
            currentWeapon = null;
        }

        // Handle "empty hands" state
        if (index == -1)
        {
            currentWeaponIndex = -1;
            if (playerAnimator != null)
            {
                playerAnimator.SetInteger("WeaponType", -1); // -1 for empty hands (character animation)
            }
            Debug.Log("Equipped: Empty Hands");
            inventoryManager?.SetCurrentWeapon(-1); // Inform inventory manager

            // Disable previous weapon's rig GameObject if it exists
            if (previousWeaponStats != null && previousWeaponStats.weaponRigGameObject != null)
            {
                StartRigWeightTransition(previousWeaponStats.weaponRigGameObject, 0f);
            }
            // Equip animations will be triggered at the end of the method
            goto EndEquipWeapon; // Skip the rest of the weapon equipping logic
        }

        // Validate weapon index
        if (index < 0 || index >= weaponsInHand.Length)
        {
            Debug.LogError("Invalid weapon index: " + index + "!");
            return;
        }

        if (weaponsInHand[index] == null)
        {
            Debug.LogError("Weapon at index " + index + " is null! Please assign a GameObject to the 'Weapons In Hand' array.");
            return;
        }

        currentWeapon = weaponsInHand[index];
        currentWeapon.SetActive(true); // Activate the selected weapon
        currentWeaponIndex = index;

        // Find the GripPoint on the weapon
        Transform gripPoint = currentWeapon.transform.Find("GripPoint");
        if (gripPoint == null)
        {
            Debug.LogWarning($"WeaponHolder: No 'GripPoint' GameObject found on {currentWeapon.name}. Weapon will be parented directly to weaponSocket.");
            // If no GripPoint, parent directly and reset local transform
            currentWeapon.transform.SetParent(weaponSocket);
            currentWeapon.transform.localPosition = Vector3.zero;
            currentWeapon.transform.localRotation = Quaternion.identity;
        }
        else
        {
            // Temporarily parent to weaponSocket to get correct world position/rotation
            currentWeapon.transform.SetParent(weaponSocket, false);

            // Calculate offset to align gripPoint with weaponSocket
            Vector3 positionOffset = weaponSocket.position - gripPoint.position;
            Quaternion rotationOffset = weaponSocket.rotation * Quaternion.Inverse(gripPoint.rotation);

            // Apply offset to the whole weapon
            currentWeapon.transform.position += positionOffset;
            currentWeapon.transform.rotation = rotationOffset * currentWeapon.transform.rotation;

            // Finally, parent to weaponSocket so it follows animations
            currentWeapon.transform.SetParent(weaponSocket);
        }

        // Get the weapon's animator, searching in children as well
        weaponAnimator = currentWeapon.GetComponentInChildren<Animator>();
        if (weaponAnimator == null)
        {
            Debug.LogWarning("WeaponHolder: Animator not found on current weapon " + currentWeapon.name + " or its children!");
            Debug.LogWarning($"WeaponHolder: No Animator found on {currentWeapon.name} or its children. Animations will not play.");
        }
        else
        {
            Debug.Log($"WeaponHolder: Successfully found Animator on {currentWeapon.name} or its children.");
            weaponAnimator.applyRootMotion = false; // Prevent Animator from moving the weapon's transform
        }
        // Store the weapon's default local position and rotation for recoil
        defaultWeaponLocalPosition = currentWeapon.transform.localPosition;
        defaultWeaponLocalRotation = currentWeapon.transform.localRotation;

        // Store the IK target's default position and rotation if available
        // Convert them to weaponSocket's local space for consistent interpolation
        if (rightHandIKTarget != null)
        {
            defaultRightIKTargetPosition = weaponSocket.InverseTransformPoint(rightHandIKTarget.position);
            defaultRightIKTargetRotation = Quaternion.Inverse(weaponSocket.rotation) * rightHandIKTarget.rotation;
            Debug.Log($"WeaponHolder: Default Right IK Target Position: {defaultRightIKTargetPosition}, Rotation: {defaultRightIKTargetRotation}");
        }
        if (leftHandIKTarget != null)
        {
            defaultLeftIKTargetPosition = weaponSocket.InverseTransformPoint(leftHandIKTarget.position);
            defaultLeftIKTargetRotation = Quaternion.Inverse(weaponSocket.rotation) * leftHandIKTarget.rotation;
            Debug.Log($"WeaponHolder: Default Left IK Target Position: {defaultLeftIKTargetPosition}, Rotation: {defaultLeftIKTargetRotation}");
        }

        // Initialize current target positions/rotations to default
        currentWeaponTargetLocalPosition = defaultWeaponLocalPosition;
        currentWeaponTargetLocalRotation = defaultWeaponLocalRotation;
        currentRightIKTargetLocalPosition = defaultRightIKTargetPosition;
        currentRightIKTargetLocalRotation = defaultRightIKTargetRotation;
        currentLeftIKTargetLocalPosition = defaultLeftIKTargetPosition;
        currentLeftIKTargetLocalRotation = defaultLeftIKTargetRotation;

        // Inform inventory manager about the new weapon
        inventoryManager?.SetCurrentWeapon(currentWeaponIndex);


        // Update character animator with current weapon type
        if (playerAnimator != null)
        {
            playerAnimator.SetInteger("WeaponType", currentWeaponIndex); // 0 for pistol, 1 for knife, -1 for empty hands
            WeaponStats currentStats = inventoryManager.GetCurrentWeaponStats();
            if (currentStats != null)
            {
                if (currentStats.hasEquipOverride)
                {
                    playerAnimator.SetTrigger("Equip_Override"); // Trigger specific character equip animation
                }

                // Handle weapon rig GameObjects
                if (previousWeaponStats != null && previousWeaponStats.weaponRigGameObject != null)
                {
                    StartRigWeightTransition(previousWeaponStats.weaponRigGameObject, 0f); // Disable previous rig GameObject
                }
                if (currentStats.weaponRigGameObject != null)
                {
                    StartRigWeightTransition(currentStats.weaponRigGameObject, 1f); // Enable new rig GameObject
                }
            }
        }

    EndEquipWeapon: // Label for goto statement
        // Always trigger generic player equip animation
        if (playerAnimator != null)
        {
            playerAnimator.SetTrigger("Equip");
        }

        // Trigger weapon equip animation if a weapon is equipped
        if (currentWeaponIndex != -1 && weaponAnimator != null)
        {
            Debug.Log($"WeaponHolder: Triggering 'Equip' animation for {currentWeapon.name}.");
            weaponAnimator.SetTrigger("Equip");
        }
        else if (currentWeaponIndex != -1)
        {
            Debug.LogWarning($"WeaponHolder: Cannot trigger 'Equip' animation for {currentWeapon.name} because weaponAnimator is null.");
        }
        Debug.Log("Equipped weapon: " + (currentWeaponIndex == -1 ? "Empty Hands" : currentWeapon.name) + " (Index: " + currentWeaponIndex + ")");
    }

    public void SetAimingState(bool isAiming)
    {
        if (currentWeapon == null || inventoryManager == null) return;

        WeaponStats currentStats = inventoryManager.GetCurrentWeaponStats();
        if (currentStats == null) return;

        if (isAiming && currentStats.aimingPositionReference != null)
        {
            Debug.Log($"WeaponHolder: SetAimingState(true) - Aiming with {currentStats.weaponName}.");
            if (playerAnimator != null)
            {
                playerAnimator.SetBool("Aiming", true);
                Debug.Log("WeaponHolder: Set playerAnimator 'Aiming' to true.");
            }
            // Calculate the world position/rotation of the aiming reference
            // Then convert it to local position/rotation relative to weaponSocket
            currentWeaponTargetLocalPosition = weaponSocket.InverseTransformPoint(currentStats.aimingPositionReference.position);
            currentWeaponTargetLocalRotation = Quaternion.Inverse(weaponSocket.rotation) * currentStats.aimingPositionReference.rotation;
            Debug.Log($"WeaponHolder: Weapon Target Position: {currentWeaponTargetLocalPosition}, Rotation: {currentWeaponTargetLocalRotation}");

            // Set target for IK hands to aiming position (relative to weaponSocket)
            if (rightHandIKTarget != null)
            {
                if (currentStats.aimingRightHandIKTargetReference != null)
                {
                    currentRightIKTargetLocalPosition = weaponSocket.InverseTransformPoint(currentStats.aimingRightHandIKTargetReference.position);
                    currentRightIKTargetLocalRotation = Quaternion.Inverse(weaponSocket.rotation) * currentStats.aimingRightHandIKTargetReference.rotation;
                }
                else
                {
                    currentRightIKTargetLocalPosition = defaultRightIKTargetPosition;
                    currentRightIKTargetLocalRotation = defaultRightIKTargetRotation;
                }
            }
            if (leftHandIKTarget != null)
            {
                if (currentStats.aimingLeftHandIKTargetReference != null)
                {
                    currentLeftIKTargetLocalPosition = weaponSocket.InverseTransformPoint(currentStats.aimingLeftHandIKTargetReference.position);
                    currentLeftIKTargetLocalRotation = Quaternion.Inverse(weaponSocket.rotation) * currentStats.aimingLeftHandIKTargetReference.rotation;
                }
                else
                {
                    currentLeftIKTargetLocalPosition = defaultLeftIKTargetPosition;
                    currentLeftIKTargetLocalRotation = defaultLeftIKTargetRotation;
                }
            }
            Debug.Log($"WeaponHolder: Right IK Target Position: {currentRightIKTargetLocalPosition}, Rotation: {currentRightIKTargetLocalRotation}");
            Debug.Log($"WeaponHolder: Left IK Target Position: {currentLeftIKTargetLocalPosition}, Rotation: {currentLeftIKTargetLocalRotation}");
            if (rightHandIKTarget != null)
            {
                Debug.Log($"WeaponHolder: Current Right IK Target Local Position (at SetAimingState): {rightHandIKTarget.localPosition}");
            }
            if (leftHandIKTarget != null)
            {
                Debug.Log($"WeaponHolder: Current Left IK Target Local Position (at SetAimingState): {leftHandIKTarget.localPosition}");
            }
        }
        else
        {
            Debug.Log($"WeaponHolder: SetAimingState(false) - Returning to default for {currentStats.weaponName}.");
            if (playerAnimator != null)
            {
                playerAnimator.SetBool("Aiming", false);
                Debug.Log("WeaponHolder: Set playerAnimator 'Aiming' to false.");
            }
            // Return to default position
            currentWeaponTargetLocalPosition = defaultWeaponLocalPosition;
            currentWeaponTargetLocalRotation = defaultWeaponLocalRotation;
            currentRightIKTargetLocalPosition = defaultRightIKTargetPosition;
            currentRightIKTargetLocalRotation = defaultRightIKTargetRotation;
            currentLeftIKTargetLocalPosition = defaultLeftIKTargetPosition;
            currentLeftIKTargetLocalRotation = defaultLeftIKTargetRotation;
            Debug.Log($"WeaponHolder: Weapon Default Position: {currentWeaponTargetLocalPosition}, Rotation: {currentWeaponTargetLocalRotation}");
            Debug.Log($"WeaponHolder: Right IK Default Position: {currentRightIKTargetLocalPosition}, Rotation: {currentRightIKTargetLocalRotation}");
            Debug.Log($"WeaponHolder: Left IK Default Position: {currentLeftIKTargetLocalPosition}, Rotation: {currentLeftIKTargetLocalRotation}");
            if (rightHandIKTarget != null)
            {
                Debug.Log($"WeaponHolder: Current Right IK Target Local Position (at SetAimingState): {rightHandIKTarget.localPosition}");
            }
            if (leftHandIKTarget != null)
            {
                Debug.Log($"WeaponHolder: Current Left IK Target Local Position (at SetAimingState): {leftHandIKTarget.localPosition}");
            }
        }
    }

    void LateUpdate()
    {
        if (currentWeapon != null && !isRecoiling) // Only interpolate if not recoiling
        {
            // Smoothly move weapon to target position/rotation
            currentWeapon.transform.localPosition = Vector3.Lerp(currentWeapon.transform.localPosition, currentWeaponTargetLocalPosition, Time.deltaTime * aimTransitionSpeed);
            currentWeapon.transform.localRotation = Quaternion.Slerp(currentWeapon.transform.localRotation, currentWeaponTargetLocalRotation, Time.deltaTime * aimTransitionSpeed);

            // Smoothly move IK targets to target position/rotation
            if (rightHandIKTarget != null)
            {
                Vector3 oldRightPos = rightHandIKTarget.localPosition;
                Quaternion oldRightRot = rightHandIKTarget.localRotation;

                rightHandIKTarget.localPosition = Vector3.Lerp(rightHandIKTarget.localPosition, currentRightIKTargetLocalPosition, Time.deltaTime * aimTransitionSpeed);
                rightHandIKTarget.localRotation = Quaternion.Slerp(rightHandIKTarget.localRotation, currentRightIKTargetLocalRotation, Time.deltaTime * aimTransitionSpeed);
                Debug.Log($"WeaponHolder: Right IK Target - Old Pos: {oldRightPos}, New Pos: {rightHandIKTarget.localPosition}, Target Pos: {currentRightIKTargetLocalPosition}");
                Debug.Log($"WeaponHolder: Right IK Target - Old Rot: {oldRightRot.eulerAngles}, New Rot: {rightHandIKTarget.localRotation.eulerAngles}, Target Rot: {currentRightIKTargetLocalRotation.eulerAngles}");
            }
            if (leftHandIKTarget != null)
            {
                Vector3 oldLeftPos = leftHandIKTarget.localPosition;
                Quaternion oldLeftRot = leftHandIKTarget.localRotation;

                leftHandIKTarget.localPosition = Vector3.Lerp(leftHandIKTarget.localPosition, currentLeftIKTargetLocalPosition, Time.deltaTime * aimTransitionSpeed);
                leftHandIKTarget.localRotation = Quaternion.Slerp(leftHandIKTarget.localRotation, currentLeftIKTargetLocalRotation, Time.deltaTime * aimTransitionSpeed);
                Debug.Log($"WeaponHolder: Left IK Target - Old Pos: {oldLeftPos}, New Pos: {leftHandIKTarget.localPosition}, Target Pos: {currentLeftIKTargetLocalPosition}");
                Debug.Log($"WeaponHolder: Left IK Target - Old Rot: {oldLeftRot.eulerAngles}, New Rot: {leftHandIKTarget.localRotation.eulerAngles}, Target Rot: {currentLeftIKTargetLocalRotation.eulerAngles}");
            }
        }
    }

    public void Shoot()
    {
        if (currentWeaponIndex == -1 || inventoryManager == null)
        {
            Debug.Log("Cannot shoot: No weapon equipped or InventoryManager not found.");
            return;
        }

        WeaponStats currentStats = inventoryManager.GetCurrentWeaponStats();
        if (currentStats == null) return;

        if (Time.time >= nextFireTime && inventoryManager.CanShoot())
        {
            nextFireTime = Time.time + currentStats.fireRate;
            inventoryManager.ConsumeAmmo();
            Debug.Log(currentStats.weaponName + " Fired!");

            // Trigger weapon shooting animation
            if (weaponAnimator != null)
            {
                Debug.Log($"WeaponHolder: Triggering 'Fire' animation for {currentStats.weaponName}.");
                weaponAnimator.SetTrigger("Fire"); // Trigger generic fire animation
                if (currentStats.currentAmmoInClip == 1) // If this is the last shot
                {
                    Debug.Log($"WeaponHolder: Triggering 'FireOut' animation for {currentStats.weaponName} (last shot).");
                    weaponAnimator.SetTrigger("FireOut"); // Trigger fire out animation
                }
            }
            else
            {
                Debug.LogWarning($"WeaponHolder: Cannot trigger 'Fire' animation for {currentStats.weaponName} because weaponAnimator is null.");
            }
            StartRecoilAnimation();

            // Play random shoot sound from the list
            if (currentStats.shootSounds != null && currentStats.shootSounds.Count > 0)
            {
                AudioSource weaponAudioSource = GetComponent<AudioSource>();
                if (weaponAudioSource == null)
                {
                    weaponAudioSource = gameObject.AddComponent<AudioSource>();
                    weaponAudioSource.spatialBlend = 1.0f; // Make it a 3D sound
                    weaponAudioSource.rolloffMode = AudioRolloffMode.Logarithmic;
                    weaponAudioSource.maxDistance = 20f;
                    weaponAudioSource.volume = 0.7f;
                }
                int randomIndex = Random.Range(0, currentStats.shootSounds.Count);
                weaponAudioSource.PlayOneShot(currentStats.shootSounds[randomIndex]);
            }
            else
            {
                Debug.LogWarning("Shoot sounds list is empty for " + currentStats.weaponName + "!");
            }

            if (currentStats.projectilePrefab != null)
            {
                // Projectile shooting
                if (Camera.main != null)
                {
                    GameObject projectile = Instantiate(currentStats.projectilePrefab, Camera.main.transform.position, Camera.main.transform.rotation);
                    Rigidbody rb = projectile.GetComponent<Rigidbody>();
                    if (rb != null)
                    {
                        rb.velocity = Camera.main.transform.forward * currentStats.projectileSpeed;
                    }
                    // You might want to add a script to the projectile to handle damage on collision
                }
            }
            else // Raycast shooting
            {
                RaycastHit hit;
                if (Camera.main != null && Physics.Raycast(Camera.main.transform.position, Camera.main.transform.forward, out hit, Mathf.Infinity))
                {
                    Debug.Log(currentStats.weaponName + " Hit: " + hit.collider.name);
                    // Example: Apply damage to a target
                    // Target target = hit.transform.GetComponent<Target>();
                    // if (target != null)
                    // {
                    //     target.TakeDamage(currentStats.damage);
                    // }
                }
            }
            // Add particle effects
        }
        else if (!inventoryManager.CanShoot())
        {
            if (currentStats.currentAmmoInClip == 0 && !currentStats.isReloading)
            {
                Debug.Log("Out of ammo! Reloading...");
                inventoryManager.StartReload();
            }
            else if (currentStats.isReloading)
            {
                Debug.Log("Cannot shoot while reloading.");
            }
        }
    }

    public void Slash()
    {
        if (currentWeaponIndex == -1 || inventoryManager == null)
        {
            Debug.Log("Cannot slash: No weapon equipped or InventoryManager not found.");
            return;
        }

        WeaponStats currentStats = inventoryManager.GetCurrentWeaponStats();
        if (currentStats == null) return;

        if (currentWeaponIndex == 1) // Assuming 1 is the knife
        {
            Debug.Log("Knife Slashing!");
            // Trigger weapon slashing animation
            if (weaponAnimator != null)
            {
                Debug.Log($"WeaponHolder: Triggering 'Slash' animation for {currentStats.weaponName}.");
                weaponAnimator.SetTrigger("Slash");
            }
            else
            {
                Debug.LogWarning($"WeaponHolder: Cannot trigger 'Slash' animation for {currentStats.weaponName} because weaponAnimator is null.");
            }

            // Perform a sphere cast or overlap sphere for knife hit detection
            // Assuming the knife's position is a good origin for the slash
            Collider[] hitColliders = Physics.OverlapSphere(currentWeapon.transform.position, knifeRange);
            foreach (var hitCollider in hitColliders)
            {
                // Don't hit self or the weapon itself
                if (hitCollider.gameObject != playerController.gameObject && !hitCollider.transform.IsChildOf(currentWeapon.transform))
                {
                    Debug.Log("Knife Hit: " + hitCollider.name);
                    // Example: Apply damage to a target
                    // Target target = hitCollider.GetComponent<Target>();
                    // if (target != null)
                    // {
                    //     target.TakeDamage(currentStats.damage); // Use damage from InventoryManager
                    // }
                }
            }
            // Add sound effects
        }
        else
        {
    Debug.Log("Cannot slash with current weapon.");
        }
    }

    void StartRecoilAnimation()
    {
        if (recoilCoroutine != null)
        {
            StopCoroutine(recoilCoroutine);
        }
        recoilCoroutine = StartCoroutine(RecoilAnimationCoroutine());
    }

    System.Collections.IEnumerator RecoilAnimationCoroutine()
    {
        if (currentWeapon == null)
        {
            Debug.LogWarning("No current weapon equipped, cannot perform recoil animation.");
            yield break;
        }

        isRecoiling = true; // Set flag at the start of recoil

        if (rightHandIKTarget != null)
        {
            // Use IK target for recoil to move the hand
            Vector3 recoilTargetPosition = currentRightIKTargetLocalPosition + recoilKickBack;
            Quaternion recoilTargetRotation = currentRightIKTargetLocalRotation * Quaternion.Euler(recoilRotation);

            float timer = 0f;
            while (timer < recoilAnimationDuration)
            {
                float progress = timer / recoilAnimationDuration;
                Vector3 currentRightHandPos = Vector3.Lerp(currentRightIKTargetLocalPosition, recoilTargetPosition, progress);
                Quaternion currentRightHandRot = Quaternion.Slerp(currentRightIKTargetLocalRotation, recoilTargetRotation, progress);

                rightHandIKTarget.localPosition = currentRightHandPos;
                rightHandIKTarget.localRotation = currentRightHandRot;

                if (leftHandIKTarget != null)
                {
                    Vector3 recoilLeftTargetPosition = currentLeftIKTargetLocalPosition + recoilKickBack;
                    Quaternion recoilLeftTargetRotation = currentLeftIKTargetLocalRotation * Quaternion.Euler(recoilRotation);
                    leftHandIKTarget.localPosition = Vector3.Lerp(currentLeftIKTargetLocalPosition, recoilLeftTargetPosition, progress);
                    leftHandIKTarget.localRotation = Quaternion.Slerp(currentLeftIKTargetLocalRotation, recoilLeftTargetRotation, progress);
                }
                timer += Time.deltaTime;
                yield return null;
            }

            // Smoothly return to original position and rotation
            timer = 0f;
            Vector3 startRecoilPosition = rightHandIKTarget.localPosition; // This is the peak recoil position
            Quaternion startRecoilQuaternion = rightHandIKTarget.localRotation; // This is the peak recoil rotation

            Vector3 startLeftRecoilPosition = leftHandIKTarget != null ? leftHandIKTarget.localPosition : Vector3.zero;
            Quaternion startLeftRecoilQuaternion = leftHandIKTarget != null ? leftHandIKTarget.localRotation : Quaternion.identity;


            while (timer < 1f) // Use a fixed duration for recovery or a speed-based approach
            {
                float progress = timer / 1f; // Recovery over 1 second, adjust as needed
                Vector3 currentRightHandPos = Vector3.Lerp(startRecoilPosition, currentRightIKTargetLocalPosition, progress);
                Quaternion currentRightHandRot = Quaternion.Slerp(startRecoilQuaternion, currentRightIKTargetLocalRotation, progress);

                rightHandIKTarget.localPosition = currentRightHandPos;
                rightHandIKTarget.localRotation = currentRightHandRot;

                if (leftHandIKTarget != null)
                {
                    leftHandIKTarget.localPosition = Vector3.Lerp(startLeftRecoilPosition, currentLeftIKTargetLocalPosition, progress);
                    leftHandIKTarget.localRotation = Quaternion.Slerp(startLeftRecoilQuaternion, currentLeftIKTargetLocalRotation, progress);
                }
                timer += Time.deltaTime * recoilRecoverySpeed; // Use recovery speed
                yield return null;
            }

            rightHandIKTarget.localPosition = currentRightIKTargetLocalPosition; // Ensure it snaps back to original
            rightHandIKTarget.localRotation = currentRightIKTargetLocalRotation; // Ensure it snaps back to original
            if (leftHandIKTarget != null)
            {
                leftHandIKTarget.localPosition = currentLeftIKTargetLocalPosition; // Ensure it snaps back to original
                leftHandIKTarget.localRotation = currentLeftIKTargetLocalRotation; // Ensure it snaps back to original
            }
        }
        else
        {
            // Fallback to moving the weapon transform directly
            Vector3 recoilTargetPosition = currentWeaponTargetLocalPosition + recoilKickBack;
            Quaternion recoilTargetRotation = currentWeaponTargetLocalRotation * Quaternion.Euler(recoilRotation);

            float timer = 0f;
            while (timer < recoilAnimationDuration)
            {
                float progress = timer / recoilAnimationDuration;
                currentWeapon.transform.localPosition = Vector3.Lerp(currentWeaponTargetLocalPosition, recoilTargetPosition, progress);
                currentWeapon.transform.localRotation = Quaternion.Slerp(currentWeaponTargetLocalRotation, recoilTargetRotation, progress);
                timer += Time.deltaTime;
                yield return null;
            }

            // Smoothly return to original position and rotation
            timer = 0f;
            Vector3 currentRecoilPosition = currentWeapon.transform.localPosition;
            Quaternion currentRecoilQuaternion = currentWeapon.transform.localRotation;

            while (timer < 1f) // Use a fixed duration for recovery or a speed-based approach
            {
                float progress = timer / 1f; // Recovery over 1 second, adjust as needed
                currentWeapon.transform.localPosition = Vector3.Lerp(currentRecoilPosition, currentWeaponTargetLocalPosition, progress);
                currentWeapon.transform.localRotation = Quaternion.Slerp(currentRecoilQuaternion, currentWeaponTargetLocalRotation, progress);
                timer += Time.deltaTime * recoilRecoverySpeed; // Use recovery speed
                yield return null;
            }

            currentWeapon.transform.localPosition = currentWeaponTargetLocalPosition; // Ensure it snaps back to original
            currentWeapon.transform.localRotation = currentWeaponTargetLocalRotation; // Ensure it snaps back to original
        }

        isRecoiling = false; // Reset flag at the end of recoil
    }

    private void OnReloadStartedHandler()
    {
        if (weaponAnimator == null)
        {
            Debug.LogWarning("WeaponHolder: weaponAnimator is null when OnReloadStartedHandler is called. Cannot trigger weapon reload animation.");
            return;
        }
        if (inventoryManager == null)
        {
            Debug.LogWarning("WeaponHolder: inventoryManager is null when OnReloadStartedHandler is called. Cannot trigger weapon reload animation.");
            return;
        }

        WeaponStats currentStats = inventoryManager.GetCurrentWeaponStats();
        if (currentStats == null)
        {
            Debug.LogWarning("WeaponHolder: currentStats is null when OnReloadStartedHandler is called. Cannot trigger weapon reload animation.");
            return;
        }

        if (playerAnimator != null)
        {
            Debug.Log($"WeaponHolder: Triggering 'Reload' animation on playerAnimator.");
            playerAnimator.SetTrigger("Reload"); // Trigger character reload animation
        }
        else
        {
            Debug.LogWarning("WeaponHolder: playerAnimator is null, cannot trigger character reload animation.");
        }

        if (weaponAnimator != null)
        {
            if (currentStats.currentAmmoInClip == 0)
            {
                Debug.Log($"WeaponHolder: Attempting to trigger 'ReloadEmpty' animation for {currentStats.weaponName}.");
                weaponAnimator.SetTrigger("ReloadEmpty");
            }
            else
            {
                Debug.Log($"WeaponHolder: Attempting to trigger 'ReloadTac' animation for {currentStats.weaponName}.");
                weaponAnimator.SetTrigger("ReloadTac");
            }
        }
        else
        {
            Debug.LogWarning($"WeaponHolder: weaponAnimator is null, cannot trigger reload animation for {currentStats.weaponName}.");
        }

        // Disable IK rig during reload
        if (currentStats.weaponRigGameObject != null)
        {
            StartRigWeightTransition(currentStats.weaponRigGameObject, 0f);
            Debug.Log($"WeaponHolder: Disabled IK rig for {currentStats.weaponName} during reload.");
        }
    }

    private void OnReloadFinishedHandler()
    {
        if (weaponAnimator != null)
        {
            Debug.Log("WeaponHolder: Reload finished. Resetting weapon animator triggers.");
            weaponAnimator.ResetTrigger("ReloadEmpty");
            weaponAnimator.ResetTrigger("ReloadTac");
        }
        else
        {
            Debug.LogWarning("WeaponHolder: weaponAnimator is null when OnReloadFinishedHandler is called. Cannot reset reload triggers.");
        }

        // Re-enable IK rig after reload
        if (inventoryManager != null)
        {
            WeaponStats currentStats = inventoryManager.GetCurrentWeaponStats();
            if (currentStats != null && currentStats.weaponRigGameObject != null)
            {
                StartRigWeightTransition(currentStats.weaponRigGameObject, 1f);
                Debug.Log($"WeaponHolder: Re-enabled IK rig for {currentStats.weaponName} after reload.");
            }
        }
    }

    void OnDisable()
    {
        if (inventoryManager != null)
        {
            inventoryManager.OnReloadStarted -= OnReloadStartedHandler;
            inventoryManager.OnReloadFinished -= OnReloadFinishedHandler; // Unsubscribe from reload finished event
        }
    }

    private void StartRigWeightTransition(GameObject rigGameObject, float targetWeight)
    {
        if (rigGameObject == null) return;

        Rig rigComponent = rigGameObject.GetComponent<Rig>();
        if (rigComponent == null)
        {
            Debug.LogWarning($"No Rig component found on {rigGameObject.name}. Cannot transition rig weight.");
            return;
        }

        if (rigWeightCoroutine != null)
        {
            StopCoroutine(rigWeightCoroutine);
        }
        rigWeightCoroutine = StartCoroutine(SmoothRigWeight(rigComponent, targetWeight));
    }

    private System.Collections.IEnumerator SmoothRigWeight(Rig rigComponent, float targetWeight)
    {
        float currentWeight = rigComponent.weight;
        float time = 0f;

        while (time < 1f)
        {
            time += Time.deltaTime * rigWeightTransitionSpeed;
            float newWeight = Mathf.Lerp(currentWeight, targetWeight, time);
            rigComponent.weight = newWeight;
            yield return null;
        }

        rigComponent.weight = targetWeight; // Ensure final weight is set
    }
}
