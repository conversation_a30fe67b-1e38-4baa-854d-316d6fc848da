using UnityEngine;

public class AnimationSoundPlayer : MonoBehaviour
{
    [System.Serializable]
    public class NamedClip
    {
        public string name;       // Name to use in Animation Event
        public AudioClip clip;    // The sound file
    }

    public AudioSource audioSource; 
    public NamedClip[] sounds;   // List of clips you can assign in Inspector

    public void PlaySound(string clipName)
    {
        foreach (var s in sounds)
        {
            if (s.name == clipName)
            {
                audioSource.PlayOneShot(s.clip);
                return;
            }
        }
        Debug.LogWarning($"Sound '{clipName}' not found on {gameObject.name}");
    }
}
