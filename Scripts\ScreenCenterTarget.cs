using UnityEngine;

public class ScreenCenterTarget : MonoBehaviour
{
    [SerializeField] private float targetDistance = 10f;
    [SerializeField] private PlayerController playerController; // Reference to the PlayerController

    // No longer needs a direct reference to PlayerController or its camera here
    // The camera will be passed directly when UpdateTarget is called.

    public void UpdateTarget(Camera cameraToUse)
    {
        if (cameraToUse == null)
        {
            Debug.LogWarning("Camera not provided to ScreenCenterTarget.UpdateTarget.");
            return;
        }

        Ray ray = cameraToUse.ViewportPointToRay(new Vector3(0.5f, 0.5f, 0));
        Vector3 targetPoint = ray.origin + ray.direction * targetDistance;

        // Check for non-finite values
        if (!float.IsFinite(targetPoint.x) || !float.IsFinite(targetPoint.y) || !float.IsFinite(targetPoint.z))
        {
            Debug.LogWarning("Calculated targetPoint contains non-finite values. Skipping update.");
            return;
        }

        transform.position = targetPoint;
    }
}
