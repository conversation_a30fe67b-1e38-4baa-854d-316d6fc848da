using UnityEngine;
using System.Collections.Generic;

public class FootstepSoundPlayer : MonoBehaviour
{
    [Header("Audio Clips")]
    [SerializeField] private List<AudioClip> walkFootstepSounds;
    [SerializeField] private List<AudioClip> runFootstepSounds;

    [Header("Footstep Settings")]
    [SerializeField] private float walkStepInterval = 0.4f; // Time between footsteps when walking
    [SerializeField] private float runStepInterval = 0.25f; // Time between footsteps when running

    private AudioSource audioSource;
    private PlayerController playerController;
    private float footstepTimer;

    void Awake()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
            audioSource.spatialBlend = 1.0f; // Make it a 3D sound
            audioSource.rolloffMode = AudioRolloffMode.Logarithmic;
            audioSource.maxDistance = 10f;
            audioSource.volume = 0.5f;
        }

        playerController = GetComponent<PlayerController>();
        if (playerController == null)
        {
            Debug.LogError("PlayerController component not found on the same GameObject!");
        }
    }

    void Update()
    {
        if (playerController == null) return;

        // Check if the player is moving
        bool isMoving = Mathf.Abs(Input.GetAxis("Horizontal")) > 0.1f || Mathf.Abs(Input.GetAxis("Vertical")) > 0.1f;

        if (isMoving)
        {
            footstepTimer += Time.deltaTime;

            float currentStepInterval = playerController.IsRunning ? runStepInterval : walkStepInterval;

            if (footstepTimer >= currentStepInterval)
            {
                PlayFootstepSound(playerController.IsRunning);
                footstepTimer = 0f;
            }
        }
        else
        {
            footstepTimer = 0f; // Reset timer when not moving
        }
    }

    void PlayFootstepSound(bool isRunning)
    {
        List<AudioClip> currentFootstepSounds = isRunning ? runFootstepSounds : walkFootstepSounds;

        if (currentFootstepSounds != null && currentFootstepSounds.Count > 0)
        {
            int randomIndex = Random.Range(0, currentFootstepSounds.Count);
            audioSource.PlayOneShot(currentFootstepSounds[randomIndex]);
        }
        else
        {
            Debug.LogWarning("Footstep sounds list is empty for " + (isRunning ? "running" : "walking") + "!");
        }
    }
}
