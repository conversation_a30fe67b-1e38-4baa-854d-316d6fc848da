using UnityEngine;
using UnityEngine.InputSystem; // Add this namespace
using static PlayerInputActions; // Add this to directly access action maps

public class PlayerController : MonoBehaviour
{
    // Reference to the generated Input Action Asset
    private PlayerInputActions playerInputActions;

    private Vector2 currentLookInput;
    private bool isAimingInput;
    private bool isRunningInput;

    // Sensitivity settings
    [Header("Sensitivity")]
    [SerializeField] public float mouseLookSensitivity = 100f; // Degrees per second for mouse look
    [SerializeField] public float controllerLookSensitivity = 10f; // Degrees per second for controller look
    [SerializeField] public float lookSensitivityMultiplier = 1f; // General multiplier for both

    [Header("Camera")]
    [SerializeField] public Transform playerCamera; // Reference to the player's camera transform
    [SerializeField] private float minVerticalAngle = -20f; // Minimum vertical look angle
    [SerializeField] private float maxVerticalAngle = 20f;  // Maximum vertical look angle

    private float verticalRotation = 0f; // Current vertical rotation of the camera

    [Header("Head Bob")]
    [SerializeField] private float walkBobSpeed = 14f;
    [SerializeField] private float walkBobAmount = 0.05f;
    [SerializeField] private float runBobSpeed = 18f;
    [SerializeField] private float runBobAmount = 0.1f;

    private float timer;
    private Vector3 headBobOffset = Vector3.zero; // Stores the calculated head bob offset

    [Header("Spine and Camera Alignment")]
    [SerializeField] private Vector3 cameraSpineOffset = new Vector3(0, 0.5f, -1.5f); // Offset from the spine bone

    private Transform spineBoneTransform; // Reference to the actual spine bone in the rig

    [Header("Player")]
    [SerializeField] private float walkSpeed = 6.0f;
    [SerializeField] private float runSpeed = 12.0f;
    [SerializeField] private bool runToggle = false; // If true, run is toggled; otherwise, it's held

    private bool isRunning = false;
    public bool IsRunning => isRunning; // Public property to expose the running state
    private float currentAnimatorSpeed = 0f; // Added for smooth blend tree transitions

    private WeaponHolder weaponHolder; // Reference to the WeaponHolder script
    private Animator animator; // Reference to the Animator component
    private PlayerInput playerInput; // Reference to the PlayerInput component
    private ScreenCenterTarget screenCenterTarget; // Reference to the ScreenCenterTarget script

    void Awake()
    {
        playerInput = GetComponent<PlayerInput>();
        if (playerInput == null)
        {
            Debug.LogError("PlayerInput component not found on the same GameObject!");
        }

        playerInputActions = new PlayerInputActions();

        // Register callbacks for Player Action Map (excluding Movement)
        playerInputActions.Player.Look.performed += ctx => currentLookInput = ctx.ReadValue<Vector2>();
        playerInputActions.Player.Look.canceled += ctx => currentLookInput = Vector2.zero;


        playerInputActions.Player.Aim.performed += ctx => { isAimingInput = true; Debug.Log("PlayerController: Aiming input detected (true)"); };
        playerInputActions.Player.Aim.canceled += ctx => { isAimingInput = false; Debug.Log("PlayerController: Aiming input detected (false)"); };

        if (runToggle)
        {
            playerInputActions.Player.Run.performed += ctx => isRunningInput = !isRunningInput; // Toggle run
        }
        else
        {
            playerInputActions.Player.Run.performed += ctx => isRunningInput = true; // Hold run
            playerInputActions.Player.Run.canceled += ctx => isRunningInput = false;
        }
    }

    void OnEnable()
    {
        // Enable only the relevant action maps/actions
        playerInputActions.Player.Look.Enable();
        playerInputActions.Player.Shoot.Enable();
        playerInputActions.Player.Slash.Enable();
        playerInputActions.Player.Aim.Enable();
        playerInputActions.Player.Run.Enable();
    }

    void OnDisable()
    {
        // Disable only the relevant action maps/actions
        playerInputActions.Player.Look.Disable();
        playerInputActions.Player.Shoot.Disable();
        playerInputActions.Player.Slash.Disable();
        playerInputActions.Player.Aim.Disable();
        playerInputActions.Player.Run.Disable();
    }

    void Start()
    {
        // Lock and hide cursor
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;

        // Get the WeaponHolder component
        weaponHolder = GetComponent<WeaponHolder>();
        if (weaponHolder == null)
        {
            Debug.LogError("WeaponHolder component not found on the same GameObject!");
        }

        // Get the Animator component
        animator = GetComponent<Animator>();
        if (animator == null)
        {
            Debug.LogError("Animator component not found on the same GameObject!");
        }

        // Initialize spine bone transform
        if (animator != null)
        {
            spineBoneTransform = animator.GetBoneTransform(HumanBodyBones.Spine);
            if (spineBoneTransform == null)
            {
                Debug.LogWarning("Spine bone not found. Ensure your rig has a 'Spine' bone and is configured as a Humanoid rig. Falling back to player's transform.");
                spineBoneTransform = transform;
            }
        }
        else
        {
            spineBoneTransform = transform; // Fallback if no animator
        }

        // Get the ScreenCenterTarget component
        screenCenterTarget = FindObjectOfType<ScreenCenterTarget>(); // Assuming there's only one in the scene
        if (screenCenterTarget == null)
        {
            Debug.LogWarning("ScreenCenterTarget component not found in the scene. Aim target will not update.");
        }
    }

    void Update()
    {
        HandleMovement();
        HandleLookRotation(); // Combined horizontal player and vertical camera rotation
        HandleShooting();
        HandleRunInput();
        HandleHeadBob(); // New method for head bob
        UpdateAnimatorParameters();
        HandleAiming(); // New method for aiming
    }

    void HandleMovement()
    {
        // Input for movement
        float horizontalInput = Input.GetAxis("Horizontal"); // A/D or Left/Right Arrow
        float verticalInput = Input.GetAxis("Vertical");   // W/S or Up/Down Arrow

        float currentSpeed = isRunning ? runSpeed : walkSpeed;

        // Calculate movement direction
        Vector3 moveDirection = transform.forward * verticalInput + transform.right * horizontalInput;
        moveDirection.Normalize(); // Ensure consistent speed in all directions

        // Apply movement
        transform.position += moveDirection * currentSpeed * Time.deltaTime;
    }

    void HandleLookRotation()
    {
        if (playerCamera == null)
        {
            Debug.LogError("Player Camera Transform not assigned in PlayerController!");
            return;
        }

        // Determine the correct sensitivity based on the input device
        float currentSensitivity = lookSensitivityMultiplier; // Default to general multiplier

        // Use PlayerInput's currentControlScheme to differentiate input devices
        if (playerInput != null && playerInput.currentControlScheme == "Keyboard&Mouse") // Assuming "Keyboard&Mouse" is the scheme name
        {
            currentSensitivity *= mouseLookSensitivity;
        }
        else // Assume it's a controller or other analog input
        {
            currentSensitivity *= controllerLookSensitivity;
        }

        // Horizontal rotation (Player body)
        float horizontalInput = currentLookInput.x;
        transform.Rotate(Vector3.up * horizontalInput * currentSensitivity * Time.deltaTime);

        // Vertical rotation (Camera)
        float verticalInput = currentLookInput.y;
        verticalRotation -= verticalInput * currentSensitivity * Time.deltaTime;
        verticalRotation = Mathf.Clamp(verticalRotation, minVerticalAngle, maxVerticalAngle);
        playerCamera.localRotation = Quaternion.Euler(verticalRotation, 0f, 0f);
    }

    void HandleShooting()
    {
        // Check for shooting input and if the player is not running
        if (playerInputActions.Player.Shoot.triggered && !isRunning && weaponHolder != null)
        {
            weaponHolder.Shoot();
        }

        // Check for slashing input
        if (playerInputActions.Player.Slash.triggered && weaponHolder != null)
        {
            weaponHolder.Slash();
        }
    }

    void HandleHeadBob()
    {
        // Get current speed for head bob calculation
        float currentBobSpeed = isRunning ? runBobSpeed : walkBobSpeed;
        float currentBobAmount = isRunning ? runBobAmount : walkBobAmount;

        // Only calculate head bob offset if moving
        if (Mathf.Abs(Input.GetAxis("Horizontal")) > 0.1f || Mathf.Abs(Input.GetAxis("Vertical")) > 0.1f)
        {
            timer += Time.deltaTime * currentBobSpeed;
            headBobOffset.y = Mathf.Sin(timer) * currentBobAmount;
            headBobOffset.x = Mathf.Cos(timer / 2) * currentBobAmount * 2; // Add some side movement
        }
        else
        {
            // Smoothly reset head bob offset when not moving
            timer = 0;
            headBobOffset.y = Mathf.Lerp(headBobOffset.y, 0, Time.deltaTime * currentBobSpeed);
            headBobOffset.x = Mathf.Lerp(headBobOffset.x, 0, Time.deltaTime * currentBobSpeed);
        }
        // The headBobOffset will be applied in LateUpdate
    }

    void HandleRunInput()
    {
        isRunning = isRunningInput;
    }

    void UpdateAnimatorParameters()
    {
        if (animator == null) return;

        // Calculate target movement speed for the animator
        float targetSpeed = 0f;
        if (Input.GetAxis("Horizontal") != 0 || Input.GetAxis("Vertical") != 0)
        {
            targetSpeed = isRunning ? runSpeed : walkSpeed;
        }

        // Smoothly interpolate currentAnimatorSpeed towards targetSpeed
        currentAnimatorSpeed = Mathf.Lerp(currentAnimatorSpeed, targetSpeed, Time.deltaTime * 5f); // Adjust 5f for desired smoothness

        animator.SetFloat("Speed", currentAnimatorSpeed);
        animator.SetBool("IsRunning", isRunning);
        // Placeholders for shooting and slashing parameters
        // animator.SetBool("IsShooting", false); // Will be set by WeaponHolder
        // animator.SetBool("IsSlashing", false); // Will be set by WeaponHolder
    }

    void HandleAiming()
    {
        if (weaponHolder != null)
        {
            weaponHolder.SetAimingState(isAimingInput);
        }
    }

    void LateUpdate()
    {
        // Ensure camera follows the spine bone with an offset, and apply head bob
        if (playerCamera != null && spineBoneTransform != null)
        {
            // Calculate the base desired world position for the camera relative to the spine bone
            Vector3 desiredWorldCameraPosition = spineBoneTransform.position + spineBoneTransform.TransformDirection(cameraSpineOffset);

            // Convert this world position to local position relative to the PlayerController's transform
            // Assuming playerCamera is a child of the PlayerController's GameObject
            Vector3 desiredLocalCameraPosition = transform.InverseTransformPoint(desiredWorldCameraPosition);

            // Apply head bob offset on top of the desired local position
            Vector3 finalLocalCameraPosition = desiredLocalCameraPosition + headBobOffset;

            // Smoothly move the camera to the final local position
            playerCamera.localPosition = Vector3.Lerp(playerCamera.localPosition, finalLocalCameraPosition, Time.deltaTime * 100f); // Adjust speed as needed

            // After updating the camera's position, update the screen center target
            if (screenCenterTarget != null)
            {
                // Get the Camera component from the playerCamera transform
                Camera camComponent = playerCamera.GetComponent<Camera>();
                if (camComponent != null)
                {
                    screenCenterTarget.UpdateTarget(camComponent);
                }
                else
                {
                    Debug.LogWarning("Camera component not found on playerCamera transform for ScreenCenterTarget update.");
                }
            }
        }
    }
}
